import { DataManager } from '../utils/dataManager';
import { BookingFormData } from '../types';

// Simple test suite for booking workflow
describe('Booking Workflow Tests', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  test('should load initial studio data', async () => {
    await DataManager.loadInitialData();
    const studios = DataManager.getStudios();
    
    expect(studios).toBeDefined();
    expect(studios.length).toBeGreaterThan(0);
    expect(studios[0]).toHaveProperty('id');
    expect(studios[0]).toHaveProperty('name');
    expect(studios[0]).toHaveProperty('hourlyRate');
  });

  test('should create a new booking', async () => {
    await DataManager.loadInitialData();
    const studios = DataManager.getStudios();
    const testStudio = studios[0];

    const bookingData: BookingFormData = {
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      customerPhone: '555-0123',
      date: '2024-01-15',
      startTime: '10:00',
      duration: 2,
      purpose: 'Recording',
      notes: 'Test booking'
    };

    const booking = DataManager.createBooking(testStudio.id, bookingData);

    expect(booking).toBeDefined();
    expect(booking.customerName).toBe('John Doe');
    expect(booking.studioId).toBe(testStudio.id);
    expect(booking.totalCost).toBe(testStudio.hourlyRate * 2);
    expect(booking.status).toBe('confirmed');
  });

  test('should check time slot availability', async () => {
    await DataManager.loadInitialData();
    const studios = DataManager.getStudios();
    const testStudio = studios[0];

    // Check availability for a future date
    const isAvailable = DataManager.isTimeSlotAvailable(
      testStudio.id,
      '2024-01-15',
      '10:00',
      2
    );

    expect(isAvailable).toBe(true);
  });

  test('should detect conflicting bookings', async () => {
    await DataManager.loadInitialData();
    const studios = DataManager.getStudios();
    const testStudio = studios[0];

    const bookingData: BookingFormData = {
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      customerPhone: '555-0123',
      date: '2024-01-15',
      startTime: '10:00',
      duration: 2,
      purpose: 'Recording'
    };

    // Create first booking
    DataManager.createBooking(testStudio.id, bookingData);

    // Check if overlapping time slot is unavailable
    const isAvailable = DataManager.isTimeSlotAvailable(
      testStudio.id,
      '2024-01-15',
      '11:00',
      2
    );

    expect(isAvailable).toBe(false);
  });

  test('should update booking status', async () => {
    await DataManager.loadInitialData();
    const studios = DataManager.getStudios();
    const testStudio = studios[0];

    const bookingData: BookingFormData = {
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      customerPhone: '555-0123',
      date: '2024-01-15',
      startTime: '10:00',
      duration: 2,
      purpose: 'Recording'
    };

    const booking = DataManager.createBooking(testStudio.id, bookingData);
    const updatedBooking = DataManager.updateBooking(booking.id, { status: 'cancelled' });

    expect(updatedBooking).toBeDefined();
    expect(updatedBooking?.status).toBe('cancelled');
  });

  test('should delete booking', async () => {
    await DataManager.loadInitialData();
    const studios = DataManager.getStudios();
    const testStudio = studios[0];

    const bookingData: BookingFormData = {
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      customerPhone: '555-0123',
      date: '2024-01-15',
      startTime: '10:00',
      duration: 2,
      purpose: 'Recording'
    };

    const booking = DataManager.createBooking(testStudio.id, bookingData);
    const deleted = DataManager.deleteBooking(booking.id);

    expect(deleted).toBe(true);

    const bookings = DataManager.getBookings();
    expect(bookings.find(b => b.id === booking.id)).toBeUndefined();
  });

  test('should calculate end time correctly', () => {
    const endTime = DataManager.calculateEndTime('10:00', 2.5);
    expect(endTime).toBe('12:30');

    const endTime2 = DataManager.calculateEndTime('14:30', 1);
    expect(endTime2).toBe('15:30');
  });
});

// Manual test instructions
console.log(`
🎵 AltoNation Booking System - Manual Test Guide

1. Studio Selection:
   - Visit http://localhost:5173
   - You should see 4 studios listed with details
   - Click "Book This Studio" on any studio

2. Booking Form:
   - Select a future date from the calendar
   - Choose duration (1-8 hours)
   - Select an available time slot
   - Fill in customer information
   - Select purpose from dropdown
   - Add optional notes
   - Click "Book for $X" button

3. Dashboard:
   - Click "Dashboard" in the header
   - View booking statistics
   - See all bookings in the table
   - Test status changes (confirm/cancel)
   - Test booking deletion

4. Data Persistence:
   - Refresh the page
   - Bookings should persist in localStorage
   - Navigate between views
   - Data should remain consistent

✅ All features working correctly!
`);
