import React from 'react';
import { Booking, Studio } from '../types';
import { DataManager } from '../utils/dataManager';

interface SimpleDashboardProps {
  bookings: Booking[];
  studios: Studio[];
  onBookingUpdate: () => void;
}

const SimpleDashboard: React.FC<SimpleDashboardProps> = ({ 
  bookings, 
  studios, 
  onBookingUpdate 
}) => {
  const handleDeleteBooking = (bookingId: string) => {
    if (window.confirm('Are you sure you want to delete this booking?')) {
      DataManager.deleteBooking(bookingId);
      onBookingUpdate();
    }
  };

  const handleStatusChange = (bookingId: string, newStatus: Booking['status']) => {
    DataManager.updateBooking(bookingId, { status: newStatus });
    onBookingUpdate();
  };

  const totalRevenue = bookings
    .filter(booking => booking.status === 'confirmed')
    .reduce((sum, booking) => sum + booking.totalCost, 0);

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ marginBottom: '32px' }}>
        <h2 style={{ fontSize: '30px', fontWeight: 'bold', color: '#111827', marginBottom: '8px' }}>
          Booking Dashboard
        </h2>
        <p style={{ color: '#6b7280' }}>Manage all studio bookings</p>
      </div>

      {/* Stats Cards */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '16px', 
        marginBottom: '32px' 
      }}>
        <div style={{
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ fontSize: '32px', marginRight: '16px' }}>📅</span>
            <div>
              <p style={{ fontSize: '14px', fontWeight: '500', color: '#6b7280', margin: 0 }}>
                Total Bookings
              </p>
              <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#111827', margin: 0 }}>
                {bookings.length}
              </p>
            </div>
          </div>
        </div>

        <div style={{
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ fontSize: '32px', marginRight: '16px' }}>✅</span>
            <div>
              <p style={{ fontSize: '14px', fontWeight: '500', color: '#6b7280', margin: 0 }}>
                Confirmed
              </p>
              <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#111827', margin: 0 }}>
                {bookings.filter(b => b.status === 'confirmed').length}
              </p>
            </div>
          </div>
        </div>

        <div style={{
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ fontSize: '32px', marginRight: '16px' }}>💰</span>
            <div>
              <p style={{ fontSize: '14px', fontWeight: '500', color: '#6b7280', margin: 0 }}>
                Revenue
              </p>
              <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#111827', margin: 0 }}>
                ${totalRevenue}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Bookings List */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)',
        border: '1px solid #e5e7eb',
        overflow: 'hidden'
      }}>
        <div style={{ padding: '24px', borderBottom: '1px solid #e5e7eb' }}>
          <h3 style={{ fontSize: '18px', fontWeight: '600', margin: 0 }}>
            All Bookings ({bookings.length})
          </h3>
        </div>

        {bookings.length === 0 ? (
          <div style={{ padding: '48px', textAlign: 'center' }}>
            <span style={{ fontSize: '48px', display: 'block', marginBottom: '16px' }}>📅</span>
            <h3 style={{ fontSize: '18px', fontWeight: '500', color: '#111827', marginBottom: '8px' }}>
              No bookings yet
            </h3>
            <p style={{ color: '#6b7280', margin: 0 }}>
              Bookings will appear here once customers start making reservations.
            </p>
          </div>
        ) : (
          <div style={{ padding: '0' }}>
            {bookings.map((booking, index) => (
              <div 
                key={booking.id} 
                style={{ 
                  padding: '24px', 
                  borderBottom: index < bookings.length - 1 ? '1px solid #f3f4f6' : 'none',
                  display: 'grid',
                  gridTemplateColumns: '1fr auto',
                  gap: '16px',
                  alignItems: 'center'
                }}
              >
                <div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                    <h4 style={{ fontSize: '16px', fontWeight: '600', margin: 0 }}>
                      {booking.customerName}
                    </h4>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: '500',
                      backgroundColor: booking.status === 'confirmed' ? '#dcfce7' : 
                                     booking.status === 'pending' ? '#fef3c7' : '#fee2e2',
                      color: booking.status === 'confirmed' ? '#15803d' : 
                             booking.status === 'pending' ? '#92400e' : '#991b1b'
                    }}>
                      {booking.status}
                    </span>
                  </div>
                  
                  <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '4px' }}>
                    <strong>{booking.studioName}</strong> • {booking.purpose}
                  </div>
                  
                  <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '4px' }}>
                    📅 {booking.date} • ⏰ {booking.startTime} - {booking.endTime} ({booking.duration}h)
                  </div>
                  
                  <div style={{ fontSize: '14px', color: '#6b7280' }}>
                    📧 {booking.customerEmail} • 📞 {booking.customerPhone}
                  </div>
                  
                  <div style={{ fontSize: '16px', fontWeight: '600', color: '#2563eb', marginTop: '8px' }}>
                    ${booking.totalCost}
                  </div>
                </div>

                <div style={{ display: 'flex', gap: '8px' }}>
                  {booking.status === 'pending' && (
                    <button
                      onClick={() => handleStatusChange(booking.id, 'confirmed')}
                      style={{
                        padding: '6px 12px',
                        backgroundColor: '#dcfce7',
                        color: '#15803d',
                        border: 'none',
                        borderRadius: '6px',
                        fontSize: '12px',
                        cursor: 'pointer'
                      }}
                    >
                      Confirm
                    </button>
                  )}
                  
                  {booking.status !== 'cancelled' && (
                    <button
                      onClick={() => handleStatusChange(booking.id, 'cancelled')}
                      style={{
                        padding: '6px 12px',
                        backgroundColor: '#fee2e2',
                        color: '#991b1b',
                        border: 'none',
                        borderRadius: '6px',
                        fontSize: '12px',
                        cursor: 'pointer'
                      }}
                    >
                      Cancel
                    </button>
                  )}
                  
                  <button
                    onClick={() => handleDeleteBooking(booking.id)}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: '#f3f4f6',
                      color: '#6b7280',
                      border: 'none',
                      borderRadius: '6px',
                      fontSize: '12px',
                      cursor: 'pointer'
                    }}
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleDashboard;
