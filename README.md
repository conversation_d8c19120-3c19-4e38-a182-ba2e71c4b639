# 🎵 AltoNation - Music Studio Booking System

A modern, responsive music studio booking application built with React, TypeScript, and Tailwind CSS. No authentication required - data is stored locally in JSON format.

## ✨ Features

- **Studio Browsing**: View available music studios with detailed equipment lists and pricing
- **Interactive Booking**: Calendar-based date selection with real-time availability checking
- **Customer Management**: Simple form-based customer information collection
- **Booking Dashboard**: Comprehensive admin interface for managing all bookings
- **Data Persistence**: Local JSON storage with localStorage backup
- **Responsive Design**: Mobile-friendly interface with modern styling
- **Real-time Updates**: Instant availability updates and booking confirmations

## 🏗️ Architecture

### Tech Stack
- **Frontend**: React 18 + TypeScript
- **Styling**: Tailwind CSS
- **Build Tool**: Vite
- **Icons**: Lucide React
- **Date Handling**: date-fns
- **Data Storage**: JSON files + localStorage

### Project Structure
```
src/
├── components/          # React components
│   ├── Header.tsx      # Navigation header
│   ├── StudioList.tsx  # Studio listing page
│   ├── BookingForm.tsx # Booking creation form
│   └── BookingDashboard.tsx # Admin dashboard
├── types/              # TypeScript interfaces
├── utils/              # Data management utilities
├── test/               # Test files
└── App.tsx            # Main application component

public/
└── data/              # JSON data files
    ├── studios.json   # Studio information
    └── bookings.json  # Booking records
```

## 🚀 Getting Started

### Prerequisites
- Node.js 16+ 
- npm or yarn

### Installation

1. **Clone or navigate to the project directory**
   ```bash
   cd AltoBookings
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   ```
   http://localhost:5173
   ```

## 📖 Usage Guide

### For Customers

1. **Browse Studios**
   - View available studios on the home page
   - See equipment lists, features, and hourly rates
   - Click "Book This Studio" to start booking

2. **Make a Booking**
   - Select your preferred date from the calendar
   - Choose session duration (1-8 hours)
   - Pick an available time slot
   - Fill in your contact information
   - Select the purpose of your session
   - Add any special notes
   - Confirm your booking

### For Studio Managers

1. **View Dashboard**
   - Click "Dashboard" in the header
   - See booking statistics and revenue
   - View all bookings in a sortable table

2. **Manage Bookings**
   - Filter bookings by status
   - Confirm pending bookings
   - Cancel bookings if needed
   - Delete bookings permanently

## 🎛️ Studio Information

### Studio A - Main Recording
- **Rate**: $150/hour
- **Capacity**: 8 people
- **Equipment**: SSL Console, Pro Tools HDX, Neumann mics, Steinway piano
- **Features**: Isolation booth, control room, live room

### Studio B - Mixing Suite  
- **Rate**: $120/hour
- **Capacity**: 4 people
- **Equipment**: Avid S6, Pro Tools Ultimate, Focal monitors
- **Features**: Acoustically treated, surround sound, mastering chain

### Studio C - Rehearsal Room
- **Rate**: $80/hour
- **Capacity**: 12 people
- **Equipment**: Full drum kit, Marshall/Fender amps, PA system
- **Features**: High ceilings, natural light, loading dock

### Studio D - Podcast Suite
- **Rate**: $60/hour
- **Capacity**: 4 people
- **Equipment**: Rode PodMics, Zoom PodTrak, video recording
- **Features**: Soundproof booth, comfortable seating, coffee station

## 💾 Data Management

### Storage System
- **Primary**: JSON files in `/public/data/`
- **Backup**: localStorage for persistence
- **Format**: Structured JSON with TypeScript interfaces

### Data Flow
1. Initial data loads from JSON files
2. Runtime data stored in localStorage
3. All changes persist across browser sessions
4. No server required - fully client-side

## 🧪 Testing

### Manual Testing
1. **Studio Selection**: Browse and select studios
2. **Booking Creation**: Complete the booking workflow
3. **Dashboard Management**: Test admin functions
4. **Data Persistence**: Verify data survives page refresh

### Automated Tests
```bash
npm test
```

## 🎨 Customization

### Adding New Studios
Edit `/public/data/studios.json`:
```json
{
  "id": "studio-5",
  "name": "Studio E - Live Room",
  "description": "Large live recording space",
  "hourlyRate": 200,
  "capacity": 20,
  "equipment": ["Live console", "Stage monitors"],
  "features": ["Stage lighting", "Green room"],
  "image": "/images/studio-e.jpg",
  "available": true
}
```

### Styling Changes
- Edit `src/style.css` for global styles
- Modify Tailwind classes in components
- Update `tailwind.config.js` for theme changes

## 📱 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🔧 Build & Deploy

### Production Build
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

### Deploy
The built files in `/dist` can be deployed to any static hosting service:
- Netlify
- Vercel
- GitHub Pages
- AWS S3

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**AltoNation** - Professional music studio booking made simple. 🎵
