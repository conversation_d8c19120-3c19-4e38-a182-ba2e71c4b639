import React, { useState } from 'react';
import { Studio, BookingFormData } from '../types';
import { DataManager } from '../utils/dataManager';

interface SimpleBookingFormProps {
  studio: Studio;
  onBookingComplete: (booking: any) => void;
  onBack: () => void;
}

const SimpleBookingForm: React.FC<SimpleBookingFormProps> = ({ 
  studio, 
  onBookingComplete, 
  onBack 
}) => {
  const [formData, setFormData] = useState<BookingFormData>({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    date: '',
    startTime: '10:00',
    duration: 2,
    purpose: 'Recording',
    notes: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    const newErrors: Record<string, string> = {};
    if (!formData.customerName.trim()) newErrors.customerName = 'Name is required';
    if (!formData.customerEmail.trim()) newErrors.customerEmail = 'Email is required';
    if (!formData.customerPhone.trim()) newErrors.customerPhone = 'Phone is required';
    if (!formData.date) newErrors.date = 'Date is required';

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsSubmitting(true);
    try {
      const booking = DataManager.createBooking(studio.id, formData);
      console.log('Booking created:', booking);
      onBookingComplete(booking);
    } catch (error) {
      console.error('Error creating booking:', error);
      setErrors({ submit: 'Failed to create booking. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalCost = studio.hourlyRate * formData.duration;

  return (
    <div style={{ maxWidth: '600px', margin: '0 auto' }}>
      <div style={{ marginBottom: '24px' }}>
        <button 
          onClick={onBack}
          style={{
            padding: '8px 16px',
            backgroundColor: '#f3f4f6',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            marginBottom: '16px'
          }}
        >
          ← Back to Studios
        </button>
        
        <div style={{
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '12px',
          boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',
            color: 'white',
            padding: '24px',
            borderRadius: '8px',
            marginBottom: '24px'
          }}>
            <h2 style={{ fontSize: '24px', fontWeight: 'bold', margin: '0 0 8px 0' }}>
              {studio.name}
            </h2>
            <p style={{ margin: '0 0 16px 0', opacity: 0.9 }}>
              {studio.description}
            </p>
            <div style={{ fontSize: '14px' }}>
              ${studio.hourlyRate}/hour • Up to {studio.capacity} people
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                Full Name *
              </label>
              <input
                type="text"
                value={formData.customerName}
                onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '16px'
                }}
                placeholder="Enter your full name"
              />
              {errors.customerName && (
                <p style={{ color: '#dc2626', fontSize: '14px', margin: '4px 0 0 0' }}>
                  {errors.customerName}
                </p>
              )}
            </div>

            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                Email *
              </label>
              <input
                type="email"
                value={formData.customerEmail}
                onChange={(e) => setFormData(prev => ({ ...prev, customerEmail: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '16px'
                }}
                placeholder="Enter your email"
              />
              {errors.customerEmail && (
                <p style={{ color: '#dc2626', fontSize: '14px', margin: '4px 0 0 0' }}>
                  {errors.customerEmail}
                </p>
              )}
            </div>

            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                Phone Number *
              </label>
              <input
                type="tel"
                value={formData.customerPhone}
                onChange={(e) => setFormData(prev => ({ ...prev, customerPhone: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '16px'
                }}
                placeholder="Enter your phone number"
              />
              {errors.customerPhone && (
                <p style={{ color: '#dc2626', fontSize: '14px', margin: '4px 0 0 0' }}>
                  {errors.customerPhone}
                </p>
              )}
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
              <div>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                  Date *
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                  min={new Date().toISOString().split('T')[0]}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '16px'
                  }}
                />
                {errors.date && (
                  <p style={{ color: '#dc2626', fontSize: '14px', margin: '4px 0 0 0' }}>
                    {errors.date}
                  </p>
                )}
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                  Start Time
                </label>
                <select
                  value={formData.startTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '16px'
                  }}
                >
                  {Array.from({ length: 14 }, (_, i) => {
                    const hour = 9 + i;
                    const time = `${hour.toString().padStart(2, '0')}:00`;
                    return (
                      <option key={time} value={time}>
                        {time}
                      </option>
                    );
                  })}
                </select>
              </div>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                Duration
              </label>
              <div style={{ display: 'flex', gap: '8px' }}>
                {[1, 2, 3, 4, 6, 8].map((hours) => (
                  <button
                    key={hours}
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, duration: hours }))}
                    style={{
                      padding: '8px 16px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      backgroundColor: formData.duration === hours ? '#2563eb' : 'white',
                      color: formData.duration === hours ? 'white' : '#374151',
                      cursor: 'pointer',
                      fontSize: '14px'
                    }}
                  >
                    {hours}h
                  </button>
                ))}
              </div>
            </div>

            <div style={{ marginBottom: '24px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                Purpose
              </label>
              <select
                value={formData.purpose}
                onChange={(e) => setFormData(prev => ({ ...prev, purpose: e.target.value }))}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '16px'
                }}
              >
                <option value="Recording">Recording Session</option>
                <option value="Mixing">Mixing & Mastering</option>
                <option value="Rehearsal">Band Rehearsal</option>
                <option value="Podcast">Podcast Recording</option>
                <option value="Voice Over">Voice Over Work</option>
                <option value="Other">Other</option>
              </select>
            </div>

            <div style={{
              backgroundColor: '#f9fafb',
              padding: '16px',
              borderRadius: '8px',
              marginBottom: '24px'
            }}>
              <h3 style={{ fontSize: '16px', fontWeight: '500', margin: '0 0 8px 0' }}>
                Booking Summary
              </h3>
              <div style={{ fontSize: '14px', color: '#6b7280' }}>
                <p style={{ margin: '4px 0' }}><strong>Studio:</strong> {studio.name}</p>
                <p style={{ margin: '4px 0' }}><strong>Date:</strong> {formData.date || 'Not selected'}</p>
                <p style={{ margin: '4px 0' }}><strong>Time:</strong> {formData.startTime} - {DataManager.calculateEndTime(formData.startTime, formData.duration)}</p>
                <p style={{ margin: '4px 0' }}><strong>Duration:</strong> {formData.duration} hour{formData.duration !== 1 ? 's' : ''}</p>
                <p style={{ margin: '4px 0' }}>
                  <strong>Total Cost:</strong> 
                  <span style={{ fontSize: '18px', fontWeight: 'bold', color: '#2563eb' }}>
                    ${totalCost}
                  </span>
                </p>
              </div>
            </div>

            <div style={{ display: 'flex', gap: '12px' }}>
              <button
                type="button"
                onClick={onBack}
                style={{
                  flex: 1,
                  padding: '12px 24px',
                  border: '1px solid #d1d5db',
                  backgroundColor: 'white',
                  color: '#374151',
                  borderRadius: '8px',
                  fontSize: '16px',
                  cursor: 'pointer'
                }}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                style={{
                  flex: 2,
                  padding: '12px 24px',
                  backgroundColor: isSubmitting ? '#9ca3af' : '#2563eb',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: '500',
                  cursor: isSubmitting ? 'not-allowed' : 'pointer'
                }}
              >
                {isSubmitting ? 'Creating Booking...' : `Book for $${totalCost}`}
              </button>
            </div>

            {errors.submit && (
              <p style={{ color: '#dc2626', fontSize: '14px', textAlign: 'center', marginTop: '16px' }}>
                {errors.submit}
              </p>
            )}
          </form>
        </div>
      </div>
    </div>
  );
};

export default SimpleBookingForm;
