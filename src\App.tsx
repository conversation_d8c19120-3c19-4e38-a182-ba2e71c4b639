import React, { useState, useEffect } from 'react';
import { Studio, Booking } from './types';
import { DataManager } from './utils/dataManager';
import StudioList from './components/StudioList';
import SimpleBookingForm from './components/SimpleBookingForm';
import SimpleDashboard from './components/SimpleDashboard';
import Header from './components/Header';

type View = 'studios' | 'booking' | 'dashboard';

function App() {
  const [currentView, setCurrentView] = useState<View>('studios');
  const [selectedStudio, setSelectedStudio] = useState<Studio | null>(null);
  const [studios, setStudios] = useState<Studio[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initializeData = async () => {
      try {
        console.log('Initializing data...');
        await DataManager.loadInitialData();
        const loadedStudios = DataManager.getStudios();
        const loadedBookings = DataManager.getBookings();

        console.log('Loaded studios:', loadedStudios);
        console.log('Loaded bookings:', loadedBookings);

        setStudios(loadedStudios);
        setBookings(loadedBookings);
      } catch (error) {
        console.error('Error initializing data:', error);
        setError(error instanceof Error ? error.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    initializeData();
  }, []);

  const handleStudioSelect = (studio: Studio) => {
    console.log('Studio selected:', studio);
    setSelectedStudio(studio);
    setCurrentView('booking');
  };

  const handleBookingComplete = (newBooking: Booking) => {
    console.log('Booking completed:', newBooking);
    setBookings(DataManager.getBookings());
    setCurrentView('dashboard');
  };

  const handleBookingUpdate = () => {
    console.log('Booking updated');
    setBookings(DataManager.getBookings());
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'studios':
        return (
          <StudioList
            studios={studios}
            onStudioSelect={handleStudioSelect}
          />
        );
      case 'booking':
        return selectedStudio ? (
          <SimpleBookingForm
            studio={selectedStudio}
            onBookingComplete={handleBookingComplete}
            onBack={() => setCurrentView('studios')}
          />
        ) : null;
      case 'dashboard':
        return (
          <SimpleDashboard
            bookings={bookings}
            studios={studios}
            onBookingUpdate={handleBookingUpdate}
          />
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading AltoNation...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Application</h1>
          <p className="text-gray-600">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header
        currentView={currentView}
        onViewChange={setCurrentView}
        bookingCount={bookings.length}
      />
      <main className="container mx-auto px-4 py-8">
        {renderCurrentView()}
      </main>
    </div>
  );
}

export default App;
