import { Studio, Booking, BookingFormData } from '../types';

// Simulate API calls with localStorage for persistence
export class DataManager {
  private static STUDIOS_KEY = 'altonation_studios';
  private static BOOKINGS_KEY = 'altonation_bookings';

  // Default studio data
  private static DEFAULT_STUDIOS: Studio[] = [
    {
      id: "studio-1",
      name: "Studio A - Main Recording",
      description: "Professional recording studio with state-of-the-art equipment",
      hourlyRate: 150,
      capacity: 8,
      equipment: [
        "SSL 4000 E Console",
        "Pro Tools HDX",
        "Neumann U87 Microphones",
        "Yamaha NS-10M Monitors",
        "Lexicon 480L Reverb",
        "1176 Compressors",
        "Neve 1073 Preamps",
        "Steinway Grand Piano"
      ],
      features: [
        "Isolation Booth",
        "Control Room",
        "Live Room",
        "Lounge Area",
        "Kitchen Access"
      ],
      image: "/images/studio-a.jpg",
      available: true
    },
    {
      id: "studio-2",
      name: "Studio B - Mixing Suite",
      description: "Dedicated mixing and mastering studio with premium monitoring",
      hourlyRate: 120,
      capacity: 4,
      equipment: [
        "Avid S6 Control Surface",
        "Pro Tools Ultimate",
        "Focal Twin6 Monitors",
        "Antelope Audio Interfaces",
        "UAD Apollo Interfaces",
        "Manley Variable Mu",
        "API 2500 Compressor",
        "Bricasti M7 Reverb"
      ],
      features: [
        "Acoustically Treated",
        "Surround Sound Setup",
        "Mastering Chain",
        "Client Lounge"
      ],
      image: "/images/studio-b.jpg",
      available: true
    },
    {
      id: "studio-3",
      name: "Studio C - Rehearsal Room",
      description: "Spacious rehearsal room perfect for bands and live recording",
      hourlyRate: 80,
      capacity: 12,
      equipment: [
        "Full Drum Kit (DW Collectors)",
        "Marshall JCM800 Amp",
        "Fender Twin Reverb",
        "Ampeg SVT Bass Rig",
        "Shure SM57/58 Mics",
        "Mackie 32-Channel Mixer",
        "JBL SRX Monitors",
        "DI Boxes"
      ],
      features: [
        "High Ceilings",
        "Natural Light",
        "Amp Storage",
        "Instrument Storage",
        "Loading Dock Access"
      ],
      image: "/images/studio-c.jpg",
      available: true
    },
    {
      id: "studio-4",
      name: "Studio D - Podcast Suite",
      description: "Intimate podcast and voice-over recording studio",
      hourlyRate: 60,
      capacity: 4,
      equipment: [
        "Rode PodMic Microphones",
        "Zoom PodTrak P8",
        "Audio-Technica Headphones",
        "Acoustic Treatment",
        "Hindenburg Pro Software",
        "Cloudlifter Preamps",
        "Pop Filters",
        "Boom Arms"
      ],
      features: [
        "Soundproof Booth",
        "Video Recording Setup",
        "Comfortable Seating",
        "Coffee Station",
        "High-Speed Internet"
      ],
      image: "/images/studio-d.jpg",
      available: true
    }
  ];

  // Load initial data
  static async loadInitialData(): Promise<void> {
    try {
      // Load studios if not already in localStorage
      if (!localStorage.getItem(this.STUDIOS_KEY)) {
        localStorage.setItem(this.STUDIOS_KEY, JSON.stringify(this.DEFAULT_STUDIOS));
        console.log('Initialized studios with default data');
      }

      // Load bookings if not already in localStorage
      if (!localStorage.getItem(this.BOOKINGS_KEY)) {
        localStorage.setItem(this.BOOKINGS_KEY, JSON.stringify([]));
        console.log('Initialized bookings with empty array');
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
      // Initialize with default data if there's an error
      localStorage.setItem(this.STUDIOS_KEY, JSON.stringify(this.DEFAULT_STUDIOS));
      localStorage.setItem(this.BOOKINGS_KEY, JSON.stringify([]));
    }
  }

  // Studios
  static getStudios(): Studio[] {
    const studios = localStorage.getItem(this.STUDIOS_KEY);
    return studios ? JSON.parse(studios) : [];
  }

  static getStudioById(id: string): Studio | undefined {
    const studios = this.getStudios();
    return studios.find(studio => studio.id === id);
  }

  // Bookings
  static getBookings(): Booking[] {
    const bookings = localStorage.getItem(this.BOOKINGS_KEY);
    return bookings ? JSON.parse(bookings) : [];
  }

  static getBookingsByStudio(studioId: string): Booking[] {
    const bookings = this.getBookings();
    return bookings.filter(booking => booking.studioId === studioId);
  }

  static getBookingsByDate(date: string): Booking[] {
    const bookings = this.getBookings();
    return bookings.filter(booking => booking.date === date);
  }

  static createBooking(studioId: string, formData: BookingFormData): Booking {
    const studio = this.getStudioById(studioId);
    if (!studio) {
      throw new Error('Studio not found');
    }

    const booking: Booking = {
      id: `booking_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      studioId,
      studioName: studio.name,
      customerName: formData.customerName,
      customerEmail: formData.customerEmail,
      customerPhone: formData.customerPhone,
      date: formData.date,
      startTime: formData.startTime,
      endTime: this.calculateEndTime(formData.startTime, formData.duration),
      duration: formData.duration,
      totalCost: studio.hourlyRate * formData.duration,
      purpose: formData.purpose,
      notes: formData.notes,
      status: 'confirmed',
      createdAt: new Date().toISOString()
    };

    const bookings = this.getBookings();
    bookings.push(booking);
    localStorage.setItem(this.BOOKINGS_KEY, JSON.stringify(bookings));

    return booking;
  }

  static updateBooking(bookingId: string, updates: Partial<Booking>): Booking | null {
    const bookings = this.getBookings();
    const index = bookings.findIndex(booking => booking.id === bookingId);
    
    if (index === -1) {
      return null;
    }

    bookings[index] = { ...bookings[index], ...updates };
    localStorage.setItem(this.BOOKINGS_KEY, JSON.stringify(bookings));
    
    return bookings[index];
  }

  static deleteBooking(bookingId: string): boolean {
    const bookings = this.getBookings();
    const filteredBookings = bookings.filter(booking => booking.id !== bookingId);
    
    if (filteredBookings.length === bookings.length) {
      return false; // Booking not found
    }

    localStorage.setItem(this.BOOKINGS_KEY, JSON.stringify(filteredBookings));
    return true;
  }

  // Utility functions
  static calculateEndTime(startTime: string, duration: number): string {
    const [hours, minutes] = startTime.split(':').map(Number);
    const startDate = new Date();
    startDate.setHours(hours, minutes, 0, 0);
    
    const endDate = new Date(startDate.getTime() + duration * 60 * 60 * 1000);
    
    return `${endDate.getHours().toString().padStart(2, '0')}:${endDate.getMinutes().toString().padStart(2, '0')}`;
  }

  static isTimeSlotAvailable(studioId: string, date: string, startTime: string, duration: number): boolean {
    const bookings = this.getBookingsByStudio(studioId);
    const dayBookings = bookings.filter(booking => booking.date === date && booking.status !== 'cancelled');
    
    const requestedStart = this.timeToMinutes(startTime);
    const requestedEnd = requestedStart + (duration * 60);
    
    return !dayBookings.some(booking => {
      const bookingStart = this.timeToMinutes(booking.startTime);
      const bookingEnd = this.timeToMinutes(booking.endTime);
      
      return (requestedStart < bookingEnd && requestedEnd > bookingStart);
    });
  }

  private static timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }
}
